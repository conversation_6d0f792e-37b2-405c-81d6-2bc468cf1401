/**
 * 数字人路径编辑器
 */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card,
  Button,
  Space,
  Tabs,
  Form,
  // Input, // 暂时注释掉未使用的组件
  Select, // 保留，因为被 Option 使用
  // InputNumber, // 暂时注释掉未使用的组件
  // Switch, // 暂时注释掉未使用的组件
  // Divider, // 暂时注释掉未使用的组件
  // List, // 暂时注释掉未使用的组件
  Modal,
  message,
  Tooltip,
  Popconfirm,
  Tag,
  Progress
} from 'antd';
import {
  // PlusOutlined, // 暂时注释掉未使用的图标
  DeleteOutlined,
  // EditOutlined, // 暂时注释掉未使用的图标
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  // EyeOutlined, // 暂时注释掉未使用的图标
  // SettingOutlined, // 暂时注释掉未使用的图标
  SaveOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { PathCanvas } from './PathCanvas';
import { PathPointEditor } from './PathPointEditor';
import { PathPropertiesPanel } from './PathPropertiesPanel';
import { PathPreview } from './PathPreview';
import { PathValidator } from './PathValidator';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 路径数据接口
 */
interface PathData {
  id: string;
  name: string;
  avatarId: string;
  points: PathPointData[];
  loopMode: 'none' | 'loop' | 'pingpong';
  interpolation: 'linear' | 'smooth' | 'bezier' | 'spline';
  enabled: boolean;
  totalDuration: number;
  metadata: {
    createdAt: string;
    updatedAt: string;
    creator: string;
    version: number;
    description?: string;
    tags?: string[];
  };
}

/**
 * 路径点数据接口
 */
interface PathPointData {
  id: string;
  position: { x: number; y: number; z: number };
  waitTime: number;
  speed: number;
  animation: string;
  lookAt?: { x: number; y: number; z: number };
  triggers?: PathTriggerData[];
}

/**
 * 路径触发器数据接口
 */
interface PathTriggerData {
  type: 'dialogue' | 'animation' | 'sound' | 'event' | 'custom';
  data: any;
  condition?: string;
  delay?: number;
  once?: boolean;
}

/**
 * 编辑器属性
 */
interface AvatarPathEditorProps {
  /** 选中的实体ID */
  entityId?: string;
  /** 初始路径数据 */
  initialPath?: PathData;
  /** 是否只读模式 */
  readonly?: boolean;
  /** 路径变化回调 */
  onPathChange?: (path: PathData) => void;
  /** 路径保存回调 */
  onPathSave?: (path: PathData) => void;
  /** 路径删除回调 */
  onPathDelete?: (pathId: string) => void;
}

/**
 * 数字人路径编辑器组件
 */
export const AvatarPathEditor: React.FC<AvatarPathEditorProps> = ({
  entityId,
  initialPath,
  readonly = false,
  onPathChange,
  onPathSave,
  onPathDelete
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  
  // 状态管理
  const [currentPath, setCurrentPath] = useState<PathData | null>(initialPath || null);
  const [selectedPointIndex, setSelectedPointIndex] = useState<number>(-1);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [playProgress, setPlayProgress] = useState(0);
  const [activeTab, setActiveTab] = useState('canvas');
  const [showPointEditor, setShowPointEditor] = useState(false);
  // const [showPropertiesPanel, setShowPropertiesPanel] = useState(false); // 暂时注释掉未使用的状态
  const [validationResult, setValidationResult] = useState<any>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // 引用
  const canvasRef = useRef<any>(null);
  const previewRef = useRef<any>(null);

  /**
   * 初始化路径数据
   */
  const initializeNewPath = useCallback(() => {
    const newPath: PathData = {
      id: `path_${Date.now()}`,
      name: t('avatarPath.newPath'),
      avatarId: entityId || '',
      points: [],
      loopMode: 'none',
      interpolation: 'linear',
      enabled: true,
      totalDuration: 0,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        creator: 'user',
        version: 1,
        description: '',
        tags: []
      }
    };
    setCurrentPath(newPath);
    form.setFieldsValue(newPath);
  }, [entityId, t, form]);

  /**
   * 处理路径属性变化
   */
  const handlePathPropertiesChange = useCallback((values: any) => {
    if (!currentPath) return;

    const updatedPath = {
      ...currentPath,
      ...values,
      metadata: {
        ...currentPath.metadata,
        updatedAt: new Date().toISOString(),
        version: currentPath.metadata.version + 1
      }
    };

    setCurrentPath(updatedPath);
    onPathChange?.(updatedPath);
  }, [currentPath, onPathChange]);

  /**
   * 添加路径点
   */
  const handleAddPoint = useCallback((position: { x: number; y: number; z: number }) => {
    if (!currentPath || readonly) return;

    const newPoint: PathPointData = {
      id: `point_${Date.now()}`,
      position,
      waitTime: 0,
      speed: 1.0,
      animation: 'walk',
      triggers: []
    };

    const updatedPath = {
      ...currentPath,
      points: [...currentPath.points, newPoint],
      metadata: {
        ...currentPath.metadata,
        updatedAt: new Date().toISOString(),
        version: currentPath.metadata.version + 1
      }
    };

    setCurrentPath(updatedPath);
    onPathChange?.(updatedPath);
    message.success(t('avatarPath.pointAdded'));
  }, [currentPath, readonly, onPathChange, t]);

  /**
   * 删除路径点
   */
  const handleDeletePoint = useCallback((index: number) => {
    if (!currentPath || readonly) return;

    const updatedPath = {
      ...currentPath,
      points: currentPath.points.filter((_, i) => i !== index),
      metadata: {
        ...currentPath.metadata,
        updatedAt: new Date().toISOString(),
        version: currentPath.metadata.version + 1
      }
    };

    setCurrentPath(updatedPath);
    setSelectedPointIndex(-1);
    onPathChange?.(updatedPath);
    message.success(t('avatarPath.pointDeleted'));
  }, [currentPath, readonly, onPathChange, t]);

  /**
   * 更新路径点
   */
  const handleUpdatePoint = useCallback((index: number, pointData: Partial<PathPointData>) => {
    if (!currentPath || readonly) return;

    const updatedPoints = [...currentPath.points];
    updatedPoints[index] = { ...updatedPoints[index], ...pointData };

    const updatedPath = {
      ...currentPath,
      points: updatedPoints,
      metadata: {
        ...currentPath.metadata,
        updatedAt: new Date().toISOString(),
        version: currentPath.metadata.version + 1
      }
    };

    setCurrentPath(updatedPath);
    onPathChange?.(updatedPath);
  }, [currentPath, readonly, onPathChange]);

  /**
   * 开始播放路径预览
   */
  const handlePlayPreview = useCallback(() => {
    if (!currentPath || currentPath.points.length < 2) {
      message.warning(t('avatarPath.needMorePoints'));
      return;
    }

    setIsPlaying(true);
    setIsPaused(false);
    previewRef.current?.startPreview();
  }, [currentPath, t]);

  /**
   * 暂停播放
   */
  const handlePausePreview = useCallback(() => {
    setIsPaused(!isPaused);
    previewRef.current?.pausePreview();
  }, [isPaused]);

  /**
   * 停止播放
   */
  const handleStopPreview = useCallback(() => {
    setIsPlaying(false);
    setIsPaused(false);
    setPlayProgress(0);
    previewRef.current?.stopPreview();
  }, []);

  /**
   * 验证路径
   */
  const handleValidatePath = useCallback(async () => {
    if (!currentPath) return;

    setIsValidating(true);
    try {
      // 这里应该调用实际的验证服务
      const result = await validatePath(currentPath);
      setValidationResult(result);
      
      if (result.valid) {
        message.success(t('avatarPath.validationPassed'));
      } else {
        message.error(t('avatarPath.validationFailed'));
      }
    } catch (error) {
      message.error(t('avatarPath.validationError'));
    } finally {
      setIsValidating(false);
    }
  }, [currentPath, t]);

  /**
   * 保存路径
   */
  const handleSavePath = useCallback(async () => {
    if (!currentPath) return;

    setIsSaving(true);
    try {
      await onPathSave?.(currentPath);
      message.success(t('avatarPath.saved'));
    } catch (error) {
      message.error(t('avatarPath.saveError'));
    } finally {
      setIsSaving(false);
    }
  }, [currentPath, onPathSave, t]);

  /**
   * 删除路径
   */
  const handleDeletePath = useCallback(() => {
    if (!currentPath) return;

    Modal.confirm({
      title: t('avatarPath.confirmDelete'),
      content: t('avatarPath.deleteWarning'),
      onOk: () => {
        onPathDelete?.(currentPath.id);
        setCurrentPath(null);
        message.success(t('avatarPath.deleted'));
      }
    });
  }, [currentPath, onPathDelete, t]);

  // 初始化
  useEffect(() => {
    if (!currentPath && !initialPath) {
      initializeNewPath();
    }
  }, [currentPath, initialPath, initializeNewPath]);

  // 模拟路径验证函数
  const validatePath = async (path: PathData) => {
    // 模拟异步验证
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const errors: string[] = [];
    const warnings: string[] = [];

    if (path.points.length < 2) {
      errors.push(t('avatarPath.validation.needMorePoints'));
    }

    if (!path.name.trim()) {
      errors.push(t('avatarPath.validation.nameRequired'));
    }

    if (!path.avatarId.trim()) {
      warnings.push(t('avatarPath.validation.avatarIdMissing'));
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      score: Math.max(0, 100 - errors.length * 20 - warnings.length * 5)
    };
  };

  if (!currentPath) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Button type="primary" onClick={initializeNewPath}>
            {t('avatarPath.createNew')}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="avatar-path-editor">
      <Card
        title={
          <Space>
            <span>{t('avatarPath.editor')}</span>
            {validationResult && (
              <Tag color={validationResult.valid ? 'green' : 'red'}>
                {validationResult.valid ? (
                  <CheckCircleOutlined />
                ) : (
                  <ExclamationCircleOutlined />
                )}
                {t('avatarPath.validation.score')}: {validationResult.score}
              </Tag>
            )}
          </Space>
        }
        extra={
          <Space>
            <Tooltip title={t('avatarPath.validate')}>
              <Button
                icon={isValidating ? <LoadingOutlined /> : <CheckCircleOutlined />}
                onClick={handleValidatePath}
                loading={isValidating}
              />
            </Tooltip>
            <Tooltip title={t('avatarPath.save')}>
              <Button
                type="primary"
                icon={isSaving ? <LoadingOutlined /> : <SaveOutlined />}
                onClick={handleSavePath}
                loading={isSaving}
                disabled={readonly}
              />
            </Tooltip>
            <Tooltip title={t('avatarPath.delete')}>
              <Popconfirm
                title={t('avatarPath.confirmDelete')}
                onConfirm={handleDeletePath}
                disabled={readonly}
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={readonly}
                />
              </Popconfirm>
            </Tooltip>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t('avatarPath.canvas')} key="canvas">
            <PathCanvas
              ref={canvasRef}
              path={currentPath}
              selectedPointIndex={selectedPointIndex}
              readonly={readonly}
              onPointSelect={setSelectedPointIndex}
              onPointAdd={handleAddPoint}
              onPointUpdate={handleUpdatePoint}
              onPointDelete={handleDeletePoint}
            />
          </TabPane>

          <TabPane tab={t('avatarPath.properties')} key="properties">
            <PathPropertiesPanel
              path={currentPath}
              readonly={readonly}
              onChange={handlePathPropertiesChange}
            />
          </TabPane>

          <TabPane tab={t('avatarPath.preview')} key="preview">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={handlePlayPreview}
                  disabled={isPlaying || currentPath.points.length < 2}
                >
                  {t('avatarPath.play')}
                </Button>
                <Button
                  icon={<PauseCircleOutlined />}
                  onClick={handlePausePreview}
                  disabled={!isPlaying}
                >
                  {isPaused ? t('avatarPath.resume') : t('avatarPath.pause')}
                </Button>
                <Button
                  icon={<StopOutlined />}
                  onClick={handleStopPreview}
                  disabled={!isPlaying}
                >
                  {t('avatarPath.stop')}
                </Button>
              </Space>
            </div>

            {isPlaying && (
              <Progress
                percent={playProgress * 100}
                status={isPaused ? 'exception' : 'active'}
                style={{ marginBottom: 16 }}
              />
            )}

            <PathPreview
              ref={previewRef}
              path={currentPath}
              onProgressChange={setPlayProgress}
              onPlayStateChange={(playing, paused) => {
                setIsPlaying(playing);
                setIsPaused(paused);
              }}
            />
          </TabPane>

          <TabPane tab={t('avatarPath.validation')} key="validation">
            <PathValidator
              path={currentPath}
              validationResult={validationResult}
              onValidate={handleValidatePath}
            />
          </TabPane>
        </Tabs>

        {/* 路径点编辑器模态框 */}
        <Modal
          title={t('avatarPath.editPoint')}
          open={showPointEditor}
          onCancel={() => setShowPointEditor(false)}
          footer={null}
          width={600}
        >
          {selectedPointIndex >= 0 && (
            <PathPointEditor
              point={currentPath.points[selectedPointIndex]}
              readonly={readonly}
              onChange={(pointData) => handleUpdatePoint(selectedPointIndex, pointData)}
              onClose={() => setShowPointEditor(false)}
            />
          )}
        </Modal>
      </Card>
    </div>
  );
};
