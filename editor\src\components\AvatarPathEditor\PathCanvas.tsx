/**
 * 路径画布组件
 */
import React, { useRef, useEffect, useImperativeHandle, forwardRef, useState, useCallback } from 'react';
import { Button, Space, Tooltip, Select, InputNumber, Switch } from 'antd';
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import * as THREE from 'three';

const { Option } = Select;

/**
 * 路径数据接口
 */
interface PathData {
  id: string;
  name: string;
  points: PathPointData[];
  loopMode: 'none' | 'loop' | 'pingpong';
  interpolation: 'linear' | 'smooth' | 'bezier' | 'spline';
}

/**
 * 路径点数据接口
 */
interface PathPointData {
  id: string;
  position: { x: number; y: number; z: number };
  waitTime: number;
  speed: number;
  animation: string;
}

/**
 * 画布属性
 */
interface PathCanvasProps {
  /** 路径数据 */
  path: PathData;
  /** 选中的路径点索引 */
  selectedPointIndex: number;
  /** 是否只读 */
  readonly?: boolean;
  /** 路径点选择回调 */
  onPointSelect?: (index: number) => void;
  /** 路径点添加回调 */
  onPointAdd?: (position: { x: number; y: number; z: number }) => void;
  /** 路径点更新回调 */
  onPointUpdate?: (index: number, data: Partial<PathPointData>) => void;
  /** 路径点删除回调 */
  onPointDelete?: (index: number) => void;
}

/**
 * 画布引用接口
 */
export interface PathCanvasRef {
  resetView: () => void;
  zoomIn: () => void;
  zoomOut: () => void;
  focusOnPoint: (index: number) => void;
  exportImage: () => string;
}

/**
 * 路径画布组件
 */
export const PathCanvas = forwardRef<PathCanvasRef, PathCanvasProps>(({
  path,
  selectedPointIndex,
  readonly = false,
  onPointSelect,
  onPointAdd,
  onPointUpdate: _onPointUpdate, // 重命名为 _onPointUpdate 表示未使用
  onPointDelete: _onPointDelete // 重命名为 _onPointDelete 表示未使用
}, ref) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Three.js 对象
  const sceneRef = useRef<THREE.Scene>();
  const cameraRef = useRef<THREE.OrthographicCamera>();
  const rendererRef = useRef<THREE.WebGLRenderer>();
  
  // 路径相关对象
  const pathLineRef = useRef<THREE.Line>();
  const pathPointsRef = useRef<THREE.Group>();
  const gridRef = useRef<THREE.GridHelper>();
  
  // 状态
  const [viewMode, setViewMode] = useState<'2d' | '3d'>('2d');
  const [showGrid, setShowGrid] = useState(true);
  const [showLabels, setShowLabels] = useState(true);
  const [snapToGrid, setSnapToGrid] = useState(false);
  const [gridSize, setGridSize] = useState(1);
  const [zoom, setZoom] = useState(1);

  /**
   * 初始化Three.js场景
   */
  const initializeScene = useCallback(() => {
    if (!canvasRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const canvas = canvasRef.current;

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.OrthographicCamera(
      -50, 50, 50, -50, 0.1, 1000
    );
    camera.position.set(0, 50, 0);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ 
      canvas,
      antialias: true,
      alpha: true
    });
    renderer.setSize(container.clientWidth, container.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    rendererRef.current = renderer;

    // 创建网格
    const grid = new THREE.GridHelper(100, 100, 0x888888, 0xcccccc);
    scene.add(grid);
    gridRef.current = grid;

    // 创建路径点组
    const pointsGroup = new THREE.Group();
    scene.add(pointsGroup);
    pathPointsRef.current = pointsGroup;

    // 添加光照
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.4);
    directionalLight.position.set(10, 10, 5);
    scene.add(directionalLight);

    // 初始渲染
    renderer.render(scene, camera);

    // 添加事件监听
    canvas.addEventListener('click', handleCanvasClick);
    canvas.addEventListener('contextmenu', handleCanvasRightClick);
    window.addEventListener('resize', handleResize);

    return () => {
      canvas.removeEventListener('click', handleCanvasClick);
      canvas.removeEventListener('contextmenu', handleCanvasRightClick);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  /**
   * 处理画布点击
   */
  const handleCanvasClick = useCallback((event: MouseEvent) => {
    if (!cameraRef.current || !rendererRef.current || readonly) return;

    const canvas = canvasRef.current!;
    const rect = canvas.getBoundingClientRect();
    const mouse = new THREE.Vector2(
      ((event.clientX - rect.left) / rect.width) * 2 - 1,
      -((event.clientY - rect.top) / rect.height) * 2 + 1
    );

    const raycaster = new THREE.Raycaster();
    raycaster.setFromCamera(mouse, cameraRef.current);

    // 检查是否点击了路径点
    if (pathPointsRef.current) {
      const intersects = raycaster.intersectObjects(pathPointsRef.current.children);
      if (intersects.length > 0) {
        const pointIndex = pathPointsRef.current.children.indexOf(intersects[0].object);
        onPointSelect?.(pointIndex);
        return;
      }
    }

    // 如果没有点击路径点，在地面添加新点
    const groundPlane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);
    const intersectPoint = new THREE.Vector3();
    raycaster.ray.intersectPlane(groundPlane, intersectPoint);

    if (intersectPoint) {
      let position = {
        x: intersectPoint.x,
        y: 0,
        z: intersectPoint.z
      };

      // 网格对齐
      if (snapToGrid) {
        position.x = Math.round(position.x / gridSize) * gridSize;
        position.z = Math.round(position.z / gridSize) * gridSize;
      }

      onPointAdd?.(position);
    }
  }, [readonly, snapToGrid, gridSize, onPointSelect, onPointAdd]);

  /**
   * 处理画布右键点击
   */
  const handleCanvasRightClick = useCallback((event: MouseEvent) => {
    event.preventDefault();
    // 可以在这里添加右键菜单
  }, []);

  /**
   * 处理窗口大小变化
   */
  const handleResize = useCallback(() => {
    if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;

    const container = containerRef.current;
    const camera = cameraRef.current;
    const renderer = rendererRef.current;

    const width = container.clientWidth;
    const height = container.clientHeight;

    camera.left = -width / 20;
    camera.right = width / 20;
    camera.top = height / 20;
    camera.bottom = -height / 20;
    camera.updateProjectionMatrix();

    renderer.setSize(width, height);
    renderer.render(sceneRef.current!, camera);
  }, []);

  /**
   * 更新路径显示
   */
  const updatePathDisplay = useCallback(() => {
    if (!sceneRef.current || !pathPointsRef.current) return;

    const scene = sceneRef.current;
    const pointsGroup = pathPointsRef.current;

    // 清除现有的路径点
    pointsGroup.clear();

    // 移除现有的路径线
    if (pathLineRef.current) {
      scene.remove(pathLineRef.current);
    }

    if (path.points.length === 0) return;

    // 创建路径点
    path.points.forEach((point, index) => {
      const geometry = new THREE.SphereGeometry(0.5, 16, 16);
      const material = new THREE.MeshLambertMaterial({
        color: index === selectedPointIndex ? 0xff4444 : 0x4444ff
      });
      const sphere = new THREE.Mesh(geometry, material);
      sphere.position.set(point.position.x, point.position.y, point.position.z);
      pointsGroup.add(sphere);

      // 添加标签
      if (showLabels) {
        // 这里可以添加文本标签的实现
      }
    });

    // 创建路径线
    if (path.points.length > 1) {
      const points = path.points.map(p => 
        new THREE.Vector3(p.position.x, p.position.y, p.position.z)
      );

      // 根据循环模式添加额外的点
      if (path.loopMode === 'loop') {
        points.push(points[0]);
      } else if (path.loopMode === 'pingpong') {
        for (let i = points.length - 2; i >= 0; i--) {
          points.push(points[i]);
        }
      }

      const geometry = new THREE.BufferGeometry().setFromPoints(points);
      const material = new THREE.LineBasicMaterial({ 
        color: 0x00ff00,
        linewidth: 2
      });
      const line = new THREE.Line(geometry, material);
      scene.add(line);
      pathLineRef.current = line;
    }

    // 重新渲染
    if (rendererRef.current && cameraRef.current) {
      rendererRef.current.render(scene, cameraRef.current);
    }
  }, [path, selectedPointIndex, showLabels]);

  /**
   * 重置视图
   */
  const resetView = useCallback(() => {
    if (!cameraRef.current || !rendererRef.current) return;

    const camera = cameraRef.current;
    camera.position.set(0, 50, 0);
    camera.lookAt(0, 0, 0);
    setZoom(1);

    rendererRef.current.render(sceneRef.current!, camera);
  }, []);

  /**
   * 放大
   */
  const zoomIn = useCallback(() => {
    if (!cameraRef.current || !rendererRef.current) return;

    const newZoom = Math.min(zoom * 1.2, 5);
    setZoom(newZoom);

    const camera = cameraRef.current;
    camera.zoom = newZoom;
    camera.updateProjectionMatrix();

    rendererRef.current.render(sceneRef.current!, camera);
  }, [zoom]);

  /**
   * 缩小
   */
  const zoomOut = useCallback(() => {
    if (!cameraRef.current || !rendererRef.current) return;

    const newZoom = Math.max(zoom / 1.2, 0.1);
    setZoom(newZoom);

    const camera = cameraRef.current;
    camera.zoom = newZoom;
    camera.updateProjectionMatrix();

    rendererRef.current.render(sceneRef.current!, camera);
  }, [zoom]);

  /**
   * 聚焦到指定路径点
   */
  const focusOnPoint = useCallback((index: number) => {
    if (!path.points[index] || !cameraRef.current || !rendererRef.current) return;

    const point = path.points[index];
    const camera = cameraRef.current;
    
    camera.position.set(point.position.x, 50, point.position.z);
    camera.lookAt(point.position.x, 0, point.position.z);

    rendererRef.current.render(sceneRef.current!, camera);
  }, [path]);

  /**
   * 导出图像
   */
  const exportImage = useCallback(() => {
    if (!rendererRef.current) return '';
    return rendererRef.current.domElement.toDataURL('image/png');
  }, []);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    resetView,
    zoomIn,
    zoomOut,
    focusOnPoint,
    exportImage
  }));

  // 初始化
  useEffect(() => {
    const cleanup = initializeScene();
    return cleanup;
  }, [initializeScene]);

  // 更新路径显示
  useEffect(() => {
    updatePathDisplay();
  }, [updatePathDisplay]);

  // 更新网格显示
  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.visible = showGrid;
      if (rendererRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current!, cameraRef.current);
      }
    }
  }, [showGrid]);

  return (
    <div className="path-canvas">
      {/* 工具栏 */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Select value={viewMode} onChange={setViewMode} style={{ width: 80 }}>
            <Option value="2d">2D</Option>
            <Option value="3d">3D</Option>
          </Select>

          <Tooltip title={t('pathCanvas.zoomIn')}>
            <Button icon={<ZoomInOutlined />} onClick={zoomIn} />
          </Tooltip>

          <Tooltip title={t('pathCanvas.zoomOut')}>
            <Button icon={<ZoomOutOutlined />} onClick={zoomOut} />
          </Tooltip>

          <Tooltip title={t('pathCanvas.resetView')}>
            <Button icon={<ReloadOutlined />} onClick={resetView} />
          </Tooltip>

          <Tooltip title={t('pathCanvas.toggleGrid')}>
            <Button
              icon={showGrid ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={() => setShowGrid(!showGrid)}
            />
          </Tooltip>

          <Switch
            checkedChildren={t('pathCanvas.labels')}
            unCheckedChildren={t('pathCanvas.labels')}
            checked={showLabels}
            onChange={setShowLabels}
          />

          <Switch
            checkedChildren={t('pathCanvas.snap')}
            unCheckedChildren={t('pathCanvas.snap')}
            checked={snapToGrid}
            onChange={setSnapToGrid}
          />

          <InputNumber
            size="small"
            value={gridSize}
            onChange={(value) => setGridSize(value || 1)}
            min={0.1}
            max={10}
            step={0.1}
            style={{ width: 80 }}
            addonBefore={t('pathCanvas.grid')}
          />
        </Space>
      </div>

      {/* 画布容器 */}
      <div
        ref={containerRef}
        style={{
          width: '100%',
          height: '400px',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        <canvas
          ref={canvasRef}
          style={{
            display: 'block',
            cursor: readonly ? 'default' : 'crosshair'
          }}
        />

        {/* 状态信息 */}
        <div
          style={{
            position: 'absolute',
            top: 8,
            left: 8,
            background: 'rgba(255, 255, 255, 0.9)',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px'
          }}
        >
          {t('pathCanvas.points')}: {path.points.length} | 
          {t('pathCanvas.zoom')}: {(zoom * 100).toFixed(0)}%
        </div>
      </div>
    </div>
  );
});
